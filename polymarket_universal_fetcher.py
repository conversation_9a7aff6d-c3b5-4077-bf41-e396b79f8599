#!/usr/bin/env python3
"""
Polymarket Universal Trades Fetcher
===================================

A comprehensive script to fetch all trades from every Polymarket market.
Optimized for ML model training with efficient storage and data export capabilities.

Features:
- Discovers all markets automatically
- Fetches all historical trades
- Stores data in ML-optimized format
- Supports incremental updates
- Robust error handling and rate limiting
- Export to multiple formats (CSV, Parquet, JSON)

Author: AI Assistant
Date: 2025-07-10
"""

import asyncio
import aiohttp
import sqlite3
import pandas as pd
import json
import logging
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, asdict
from pathlib import Path
import argparse
import sys


@dataclass
class Trade:
    """Trade data structure optimized for ML training"""
    proxy_wallet: str
    side: str  # BUY/SELL
    asset: str
    condition_id: str
    size: float
    price: float
    timestamp: int
    title: str
    slug: str
    icon: str
    event_slug: str
    outcome: str
    outcome_index: int
    name: str
    pseudonym: str
    bio: str
    profile_image: str
    profile_image_optimized: str
    transaction_hash: str
    
    # Derived fields for ML
    datetime_utc: str = ""
    price_cents: int = 0
    size_scaled: int = 0
    hour_of_day: int = 0
    day_of_week: int = 0
    
    def __post_init__(self):
        """Calculate derived fields for ML features"""
        dt = datetime.fromtimestamp(self.timestamp)
        self.datetime_utc = dt.isoformat()
        self.price_cents = int(self.price * 10000)  # Store as basis points
        self.size_scaled = int(self.size * 1000000)  # Store with 6 decimal precision
        self.hour_of_day = dt.hour
        self.day_of_week = dt.weekday()


@dataclass
class Market:
    """Market data structure"""
    id: str
    question: str
    condition_id: str
    slug: str
    end_date: str
    liquidity: float
    volume: float
    active: bool
    closed: bool
    outcomes: List[str]
    outcome_prices: List[float]


class PolymarketUniversalFetcher:
    """Universal Polymarket trades fetcher with ML-optimized storage"""
    
    def __init__(self, db_path: str = "polymarket_universal.db", 
                 rate_limit: float = 0.1, max_concurrent: int = 10):
        self.db_path = db_path
        self.rate_limit = rate_limit
        self.max_concurrent = max_concurrent
        self.session: Optional[aiohttp.ClientSession] = None
        self.semaphore = asyncio.Semaphore(max_concurrent)
        
        # API endpoints
        self.events_api = "https://gamma-api.polymarket.com/events/pagination"
        self.trades_api = "https://data-api.polymarket.com/trades"
        
        # Setup logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('polymarket_fetcher.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
        # Initialize database
        self._init_database()
    
    def _init_database(self):
        """Initialize SQLite database with ML-optimized schema"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Markets table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS markets (
                id TEXT PRIMARY KEY,
                question TEXT NOT NULL,
                condition_id TEXT UNIQUE NOT NULL,
                slug TEXT NOT NULL,
                end_date TEXT,
                liquidity REAL,
                volume REAL,
                active BOOLEAN,
                closed BOOLEAN,
                outcomes TEXT,  -- JSON array
                outcome_prices TEXT,  -- JSON array
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # Trades table optimized for ML
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS trades (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                proxy_wallet TEXT NOT NULL,
                side TEXT NOT NULL,
                asset TEXT NOT NULL,
                condition_id TEXT NOT NULL,
                size REAL NOT NULL,
                price REAL NOT NULL,
                timestamp INTEGER NOT NULL,
                title TEXT,
                slug TEXT,
                icon TEXT,
                event_slug TEXT,
                outcome TEXT,
                outcome_index INTEGER,
                name TEXT,
                pseudonym TEXT,
                bio TEXT,
                profile_image TEXT,
                profile_image_optimized TEXT,
                transaction_hash TEXT UNIQUE,
                
                -- ML-optimized derived fields
                datetime_utc TEXT NOT NULL,
                price_cents INTEGER NOT NULL,
                size_scaled INTEGER NOT NULL,
                hour_of_day INTEGER NOT NULL,
                day_of_week INTEGER NOT NULL,
                
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # Create indexes for efficient querying
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_trades_timestamp ON trades(timestamp)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_trades_condition_id ON trades(condition_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_trades_side ON trades(side)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_trades_outcome ON trades(outcome)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_trades_datetime ON trades(datetime_utc)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_trades_tx_hash ON trades(transaction_hash)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_markets_condition_id ON markets(condition_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_markets_active ON markets(active)")
        
        # Metadata table for tracking fetch progress
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS fetch_metadata (
                key TEXT PRIMARY KEY,
                value TEXT,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        conn.commit()
        conn.close()
        self.logger.info(f"Database initialized at {self.db_path}")
    
    async def __aenter__(self):
        """Async context manager entry"""
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=30),
            connector=aiohttp.TCPConnector(limit=100)
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self.session:
            await self.session.close()
    
    async def _make_request(self, url: str, params: Dict = None) -> Dict:
        """Make rate-limited HTTP request"""
        async with self.semaphore:
            await asyncio.sleep(self.rate_limit)

            try:
                async with self.session.get(url, params=params) as response:
                    if response.status == 200:
                        return await response.json()
                    else:
                        self.logger.error(f"HTTP {response.status} for {url}")
                        return {}
            except Exception as e:
                self.logger.error(f"Request failed for {url}: {e}")
                return {}

    async def discover_all_markets(self) -> List[Market]:
        """Discover all active markets from Polymarket"""
        self.logger.info("Discovering all markets...")
        markets = []
        offset = 0
        limit = 1000

        while True:
            params = {
                'limit': limit,
                'offset': offset,
                'active': 'true',
                'archived': 'false'
            }

            data = await self._make_request(self.events_api, params)
            if not data or 'data' not in data:
                break

            events = data['data']
            if not events:
                break

            for event in events:
                if 'markets' in event:
                    for market_data in event['markets']:
                        try:
                            market = Market(
                                id=market_data['id'],
                                question=market_data['question'],
                                condition_id=market_data['conditionId'],
                                slug=market_data['slug'],
                                end_date=market_data.get('endDate', ''),
                                liquidity=float(market_data.get('liquidity', 0)),
                                volume=float(market_data.get('volume', 0)),
                                active=market_data.get('active', False),
                                closed=market_data.get('closed', False),
                                outcomes=json.loads(market_data.get('outcomes', '[]')),
                                outcome_prices=[float(p) for p in json.loads(market_data.get('outcomePrices', '[]'))]
                            )
                            markets.append(market)
                        except Exception as e:
                            self.logger.error(f"Error parsing market {market_data.get('id', 'unknown')}: {e}")

            self.logger.info(f"Discovered {len(markets)} markets so far...")

            # Check if there are more pages
            if not data.get('pagination', {}).get('hasMore', False):
                break

            offset += limit

        self.logger.info(f"Total markets discovered: {len(markets)}")
        return markets

    def save_markets(self, markets: List[Market]):
        """Save markets to database"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        for market in markets:
            cursor.execute("""
                INSERT OR REPLACE INTO markets
                (id, question, condition_id, slug, end_date, liquidity, volume,
                 active, closed, outcomes, outcome_prices, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
            """, (
                market.id, market.question, market.condition_id, market.slug,
                market.end_date, market.liquidity, market.volume, market.active,
                market.closed, json.dumps(market.outcomes), json.dumps(market.outcome_prices)
            ))

        conn.commit()
        conn.close()
        self.logger.info(f"Saved {len(markets)} markets to database")

    async def fetch_trades_for_market(self, condition_id: str, market_slug: str) -> List[Trade]:
        """Fetch all trades for a specific market"""
        trades = []
        offset = 0
        limit = 500  # Max allowed by API

        self.logger.info(f"Fetching trades for market: {market_slug}")

        while True:
            params = {
                'market': condition_id,
                'limit': limit,
                'offset': offset,
                'takerOnly': 'false'  # Get both maker and taker trades
            }

            data = await self._make_request(self.trades_api, params)
            if not data or not isinstance(data, list):
                break

            if not data:  # Empty response means no more trades
                break

            for trade_data in data:
                try:
                    trade = Trade(
                        proxy_wallet=trade_data.get('proxyWallet', ''),
                        side=trade_data.get('side', ''),
                        asset=trade_data.get('asset', ''),
                        condition_id=trade_data.get('conditionId', ''),
                        size=float(trade_data.get('size', 0)),
                        price=float(trade_data.get('price', 0)),
                        timestamp=int(trade_data.get('timestamp', 0)),
                        title=trade_data.get('title', ''),
                        slug=trade_data.get('slug', ''),
                        icon=trade_data.get('icon', ''),
                        event_slug=trade_data.get('eventSlug', ''),
                        outcome=trade_data.get('outcome', ''),
                        outcome_index=int(trade_data.get('outcomeIndex', 0)),
                        name=trade_data.get('name', ''),
                        pseudonym=trade_data.get('pseudonym', ''),
                        bio=trade_data.get('bio', ''),
                        profile_image=trade_data.get('profileImage', ''),
                        profile_image_optimized=trade_data.get('profileImageOptimized', ''),
                        transaction_hash=trade_data.get('transactionHash', '')
                    )
                    trades.append(trade)
                except Exception as e:
                    self.logger.error(f"Error parsing trade: {e}")

            self.logger.info(f"Fetched {len(trades)} trades for {market_slug}")

            # If we got less than the limit, we've reached the end
            if len(data) < limit:
                break

            offset += limit

        return trades

    def save_trades(self, trades: List[Trade]):
        """Save trades to database with conflict resolution"""
        if not trades:
            return

        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        saved_count = 0
        duplicate_count = 0

        for trade in trades:
            try:
                cursor.execute("""
                    INSERT OR IGNORE INTO trades
                    (proxy_wallet, side, asset, condition_id, size, price, timestamp,
                     title, slug, icon, event_slug, outcome, outcome_index, name,
                     pseudonym, bio, profile_image, profile_image_optimized,
                     transaction_hash, datetime_utc, price_cents, size_scaled,
                     hour_of_day, day_of_week)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    trade.proxy_wallet, trade.side, trade.asset, trade.condition_id,
                    trade.size, trade.price, trade.timestamp, trade.title, trade.slug,
                    trade.icon, trade.event_slug, trade.outcome, trade.outcome_index,
                    trade.name, trade.pseudonym, trade.bio, trade.profile_image,
                    trade.profile_image_optimized, trade.transaction_hash,
                    trade.datetime_utc, trade.price_cents, trade.size_scaled,
                    trade.hour_of_day, trade.day_of_week
                ))

                if cursor.rowcount > 0:
                    saved_count += 1
                else:
                    duplicate_count += 1

            except Exception as e:
                self.logger.error(f"Error saving trade {trade.transaction_hash}: {e}")

        conn.commit()
        conn.close()

        self.logger.info(f"Saved {saved_count} new trades, {duplicate_count} duplicates skipped")

    async def fetch_all_trades(self, batch_size: int = 10):
        """Fetch all trades from all markets"""
        # First discover all markets
        markets = await self.discover_all_markets()
        self.save_markets(markets)

        # Create batches for concurrent processing
        market_batches = [markets[i:i + batch_size] for i in range(0, len(markets), batch_size)]

        total_trades = 0

        for batch_num, batch in enumerate(market_batches, 1):
            self.logger.info(f"Processing batch {batch_num}/{len(market_batches)} ({len(batch)} markets)")

            # Process batch concurrently
            tasks = []
            for market in batch:
                task = self.fetch_trades_for_market(market.condition_id, market.slug)
                tasks.append(task)

            batch_results = await asyncio.gather(*tasks, return_exceptions=True)

            # Save trades from this batch
            for i, result in enumerate(batch_results):
                if isinstance(result, Exception):
                    self.logger.error(f"Error fetching trades for {batch[i].slug}: {result}")
                else:
                    self.save_trades(result)
                    total_trades += len(result)

            self.logger.info(f"Batch {batch_num} complete. Total trades so far: {total_trades}")

        # Update metadata
        self._update_metadata('last_full_fetch', datetime.now().isoformat())
        self._update_metadata('total_trades_fetched', str(total_trades))

        self.logger.info(f"Fetch complete! Total trades collected: {total_trades}")
        return total_trades

    def _update_metadata(self, key: str, value: str):
        """Update metadata in database"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute("""
            INSERT OR REPLACE INTO fetch_metadata (key, value, updated_at)
            VALUES (?, ?, CURRENT_TIMESTAMP)
        """, (key, value))
        conn.commit()
        conn.close()

    async def fetch_recent_trades(self, hours_back: int = 24):
        """Fetch recent trades for incremental updates"""
        self.logger.info(f"Fetching trades from last {hours_back} hours...")

        cutoff_timestamp = int((datetime.now() - timedelta(hours=hours_back)).timestamp())

        trades = []
        offset = 0
        limit = 500

        while True:
            params = {
                'limit': limit,
                'offset': offset,
                'takerOnly': 'false'
            }

            data = await self._make_request(self.trades_api, params)
            if not data or not isinstance(data, list):
                break

            if not data:
                break

            # Check if we've gone back far enough
            oldest_timestamp = min(trade.get('timestamp', float('inf')) for trade in data)
            if oldest_timestamp < cutoff_timestamp:
                # Filter trades within our time window
                recent_data = [t for t in data if t.get('timestamp', 0) >= cutoff_timestamp]
                if recent_data:
                    trades.extend(self._parse_trades(recent_data))
                break

            trades.extend(self._parse_trades(data))
            offset += limit

        self.save_trades(trades)
        self.logger.info(f"Fetched {len(trades)} recent trades")
        return len(trades)

    def _parse_trades(self, trade_data_list: List[Dict]) -> List[Trade]:
        """Parse trade data from API response"""
        trades = []
        for trade_data in trade_data_list:
            try:
                trade = Trade(
                    proxy_wallet=trade_data.get('proxyWallet', ''),
                    side=trade_data.get('side', ''),
                    asset=trade_data.get('asset', ''),
                    condition_id=trade_data.get('conditionId', ''),
                    size=float(trade_data.get('size', 0)),
                    price=float(trade_data.get('price', 0)),
                    timestamp=int(trade_data.get('timestamp', 0)),
                    title=trade_data.get('title', ''),
                    slug=trade_data.get('slug', ''),
                    icon=trade_data.get('icon', ''),
                    event_slug=trade_data.get('eventSlug', ''),
                    outcome=trade_data.get('outcome', ''),
                    outcome_index=int(trade_data.get('outcomeIndex', 0)),
                    name=trade_data.get('name', ''),
                    pseudonym=trade_data.get('pseudonym', ''),
                    bio=trade_data.get('bio', ''),
                    profile_image=trade_data.get('profileImage', ''),
                    profile_image_optimized=trade_data.get('profileImageOptimized', ''),
                    transaction_hash=trade_data.get('transactionHash', '')
                )
                trades.append(trade)
            except Exception as e:
                self.logger.error(f"Error parsing trade: {e}")
        return trades

    def export_to_csv(self, output_path: str = "polymarket_trades.csv",
                      limit: Optional[int] = None):
        """Export trades to CSV format for ML training"""
        self.logger.info(f"Exporting trades to {output_path}")

        conn = sqlite3.connect(self.db_path)

        query = """
            SELECT t.*, m.question, m.outcomes, m.volume as market_volume, m.liquidity as market_liquidity
            FROM trades t
            LEFT JOIN markets m ON t.condition_id = m.condition_id
            ORDER BY t.timestamp DESC
        """

        if limit:
            query += f" LIMIT {limit}"

        df = pd.read_sql_query(query, conn)
        conn.close()

        df.to_csv(output_path, index=False)
        self.logger.info(f"Exported {len(df)} trades to {output_path}")
        return len(df)

    def export_to_parquet(self, output_path: str = "polymarket_trades.parquet",
                         limit: Optional[int] = None):
        """Export trades to Parquet format for efficient ML training"""
        self.logger.info(f"Exporting trades to {output_path}")

        conn = sqlite3.connect(self.db_path)

        query = """
            SELECT t.*, m.question, m.outcomes, m.volume as market_volume, m.liquidity as market_liquidity
            FROM trades t
            LEFT JOIN markets m ON t.condition_id = m.condition_id
            ORDER BY t.timestamp DESC
        """

        if limit:
            query += f" LIMIT {limit}"

        df = pd.read_sql_query(query, conn)
        conn.close()

        # Optimize data types for ML
        df['timestamp'] = pd.to_datetime(df['timestamp'], unit='s')
        df['side'] = df['side'].astype('category')
        df['outcome'] = df['outcome'].astype('category')

        df.to_parquet(output_path, index=False, compression='snappy')
        self.logger.info(f"Exported {len(df)} trades to {output_path}")
        return len(df)

    def get_stats(self) -> Dict[str, Any]:
        """Get database statistics"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # Trade statistics
        cursor.execute("SELECT COUNT(*) FROM trades")
        total_trades = cursor.fetchone()[0]

        cursor.execute("SELECT COUNT(DISTINCT condition_id) FROM trades")
        markets_with_trades = cursor.fetchone()[0]

        cursor.execute("SELECT COUNT(*) FROM markets")
        total_markets = cursor.fetchone()[0]

        cursor.execute("SELECT MIN(timestamp), MAX(timestamp) FROM trades")
        time_range = cursor.fetchone()

        cursor.execute("SELECT SUM(size * price) FROM trades")
        total_volume = cursor.fetchone()[0] or 0

        cursor.execute("""
            SELECT side, COUNT(*)
            FROM trades
            GROUP BY side
        """)
        side_distribution = dict(cursor.fetchall())

        cursor.execute("""
            SELECT outcome, COUNT(*)
            FROM trades
            WHERE outcome != ''
            GROUP BY outcome
            ORDER BY COUNT(*) DESC
            LIMIT 10
        """)
        top_outcomes = dict(cursor.fetchall())

        conn.close()

        stats = {
            'total_trades': total_trades,
            'total_markets': total_markets,
            'markets_with_trades': markets_with_trades,
            'total_volume_usd': round(total_volume, 2),
            'time_range': {
                'earliest': datetime.fromtimestamp(time_range[0]).isoformat() if time_range[0] else None,
                'latest': datetime.fromtimestamp(time_range[1]).isoformat() if time_range[1] else None
            },
            'side_distribution': side_distribution,
            'top_outcomes': top_outcomes
        }

        return stats

    def print_stats(self):
        """Print formatted statistics"""
        stats = self.get_stats()

        print("\n" + "="*50)
        print("POLYMARKET UNIVERSAL FETCHER STATISTICS")
        print("="*50)
        print(f"Total Trades: {stats['total_trades']:,}")
        print(f"Total Markets: {stats['total_markets']:,}")
        print(f"Markets with Trades: {stats['markets_with_trades']:,}")
        print(f"Total Volume: ${stats['total_volume_usd']:,.2f}")

        if stats['time_range']['earliest']:
            print(f"Time Range: {stats['time_range']['earliest']} to {stats['time_range']['latest']}")

        print(f"\nTrade Side Distribution:")
        for side, count in stats['side_distribution'].items():
            print(f"  {side}: {count:,}")

        print(f"\nTop Outcomes:")
        for outcome, count in stats['top_outcomes'].items():
            print(f"  {outcome}: {count:,}")

        print("="*50)


async def main():
    """Main function with CLI interface"""
    parser = argparse.ArgumentParser(description="Polymarket Universal Trades Fetcher")
    parser.add_argument('--mode', choices=['full', 'incremental', 'stats', 'export'],
                       default='stats', help='Operation mode')
    parser.add_argument('--db-path', default='polymarket_universal.db',
                       help='Database file path')
    parser.add_argument('--hours-back', type=int, default=24,
                       help='Hours back for incremental fetch')
    parser.add_argument('--export-format', choices=['csv', 'parquet'], default='csv',
                       help='Export format')
    parser.add_argument('--export-path', help='Export file path')
    parser.add_argument('--limit', type=int, help='Limit number of records for export')
    parser.add_argument('--batch-size', type=int, default=10,
                       help='Batch size for concurrent market processing')
    parser.add_argument('--rate-limit', type=float, default=0.1,
                       help='Rate limit between requests (seconds)')

    args = parser.parse_args()

    async with PolymarketUniversalFetcher(
        db_path=args.db_path,
        rate_limit=args.rate_limit,
        max_concurrent=args.batch_size
    ) as fetcher:

        if args.mode == 'full':
            print("🚀 Starting full fetch of all Polymarket trades...")
            total_trades = await fetcher.fetch_all_trades(batch_size=args.batch_size)
            print(f"✅ Fetch complete! Collected {total_trades:,} trades")
            fetcher.print_stats()

        elif args.mode == 'incremental':
            print(f"🔄 Fetching trades from last {args.hours_back} hours...")
            new_trades = await fetcher.fetch_recent_trades(hours_back=args.hours_back)
            print(f"✅ Incremental fetch complete! Collected {new_trades:,} new trades")
            fetcher.print_stats()

        elif args.mode == 'stats':
            fetcher.print_stats()

        elif args.mode == 'export':
            if not args.export_path:
                args.export_path = f"polymarket_trades.{args.export_format}"

            if args.export_format == 'csv':
                count = fetcher.export_to_csv(args.export_path, args.limit)
            else:
                count = fetcher.export_to_parquet(args.export_path, args.limit)

            print(f"✅ Exported {count:,} trades to {args.export_path}")


if __name__ == "__main__":
    asyncio.run(main())
