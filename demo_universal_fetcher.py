#!/usr/bin/env python3
"""
Demo script for Polymarket Universal Fetcher
============================================

This script demonstrates how to use the Polymarket Universal Fetcher
to collect all trades from every market on Polymarket.

Usage examples:
    python demo_universal_fetcher.py
"""

import asyncio
import sys
from polymarket_universal_fetcher import PolymarketUniversalFetcher


async def demo_workflow():
    """Demonstrate the complete workflow"""
    print("🚀 Polymarket Universal Fetcher Demo")
    print("=" * 50)
    
    # Initialize the fetcher
    async with PolymarketUniversalFetcher(
        db_path="demo_universal.db",
        rate_limit=0.1,  # 100ms between requests
        max_concurrent=5  # Conservative for demo
    ) as fetcher:
        
        print("\n📊 Step 1: Checking current database stats...")
        fetcher.print_stats()
        
        print("\n🔍 Step 2: Discovering markets...")
        markets = await fetcher.discover_all_markets()
        print(f"Found {len(markets)} total markets")
        
        # Show some example markets
        print("\nExample markets:")
        for i, market in enumerate(markets[:5]):
            print(f"  {i+1}. {market.question[:80]}...")
        
        print("\n💾 Step 3: Saving markets to database...")
        fetcher.save_markets(markets)
        
        print("\n📈 Step 4: Fetching recent trades (last 6 hours)...")
        recent_trades = await fetcher.fetch_recent_trades(hours_back=6)
        print(f"Collected {recent_trades} recent trades")
        
        print("\n📊 Step 5: Updated database stats...")
        fetcher.print_stats()
        
        print("\n💾 Step 6: Exporting data...")
        # Export to CSV
        csv_count = fetcher.export_to_csv("demo_trades.csv", limit=1000)
        print(f"Exported {csv_count} trades to demo_trades.csv")
        
        # Export to Parquet (more efficient for ML)
        parquet_count = fetcher.export_to_parquet("demo_trades.parquet", limit=1000)
        print(f"Exported {parquet_count} trades to demo_trades.parquet")
        
        print("\n✅ Demo complete!")
        print("\nNext steps:")
        print("1. Run full fetch: python polymarket_universal_fetcher.py --mode full")
        print("2. Set up incremental updates: python polymarket_universal_fetcher.py --mode incremental")
        print("3. Export all data: python polymarket_universal_fetcher.py --mode export --export-format parquet")


async def quick_test():
    """Quick test to verify the fetcher works"""
    print("🧪 Quick Test: Fetching 5 recent trades...")
    
    async with PolymarketUniversalFetcher(
        db_path="test_universal.db",
        rate_limit=0.2,
        max_concurrent=2
    ) as fetcher:
        
        # Test the trades API directly
        trades_data = await fetcher._make_request(
            "https://data-api.polymarket.com/trades",
            {"limit": 5}
        )
        
        if trades_data:
            print(f"✅ Successfully fetched {len(trades_data)} trades")
            print("\nSample trade:")
            trade = trades_data[0]
            print(f"  Market: {trade.get('title', 'N/A')}")
            print(f"  Side: {trade.get('side', 'N/A')}")
            print(f"  Size: {trade.get('size', 'N/A')}")
            print(f"  Price: {trade.get('price', 'N/A')}")
            print(f"  Outcome: {trade.get('outcome', 'N/A')}")
        else:
            print("❌ Failed to fetch trades")
            return False
        
        # Test the events API
        events_data = await fetcher._make_request(
            "https://gamma-api.polymarket.com/events/pagination",
            {"limit": 2, "active": "true"}
        )
        
        if events_data and 'data' in events_data:
            print(f"✅ Successfully fetched {len(events_data['data'])} events")
            if events_data['data']:
                event = events_data['data'][0]
                print(f"\nSample event: {event.get('title', 'N/A')}")
        else:
            print("❌ Failed to fetch events")
            return False
        
        print("\n✅ All tests passed! The fetcher is ready to use.")
        return True


if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "--test":
        # Run quick test
        success = asyncio.run(quick_test())
        sys.exit(0 if success else 1)
    else:
        # Run full demo
        asyncio.run(demo_workflow())
