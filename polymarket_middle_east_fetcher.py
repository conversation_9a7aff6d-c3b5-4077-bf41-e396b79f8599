#!/usr/bin/env python3
"""
Polymarket Middle East Trades Fetcher

This script fetches all trades from Middle East related markets on Polymarket
using the gamma-api events endpoint with middle-east tag filtering.
Supports both open and closed markets with intelligent data storage.

Usage:
    python polymarket_middle_east_fetcher.py
"""

import requests
import json
import time
import csv
import sqlite3
import os
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Set
import logging
from dataclasses import dataclass, asdict
from functools import wraps
import hashlib

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('polymarket_fetcher.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


def retry_on_failure(max_retries: int = 3, delay: float = 1.0, backoff: float = 2.0):
    """Decorator to retry function calls on failure with exponential backoff"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            last_exception = None
            current_delay = delay

            for attempt in range(max_retries + 1):
                try:
                    return func(*args, **kwargs)
                except requests.exceptions.RequestException as e:
                    last_exception = e
                    if attempt < max_retries:
                        logger.warning(f"Attempt {attempt + 1} failed for {func.__name__}: {e}. Retrying in {current_delay}s...")
                        time.sleep(current_delay)
                        current_delay *= backoff
                    else:
                        logger.error(f"All {max_retries + 1} attempts failed for {func.__name__}")
                except Exception as e:
                    # Don't retry on non-network errors
                    logger.error(f"Non-retryable error in {func.__name__}: {e}")
                    raise

            raise last_exception
        return wrapper
    return decorator

@dataclass
class Market:
    """Data class for market information"""
    id: int
    slug: str
    title: str
    condition_id: str
    active: bool
    closed: bool
    liquidity: float
    volume: float
    start_date: str
    end_date: str

class PolymarketFetcher:
    """Main class for fetching Polymarket data"""

    def __init__(self, db_path: str = "polymarket_middle_east.db"):
        self.gamma_api_base = "https://gamma-api.polymarket.com"
        self.data_api_base = "https://data-api.polymarket.com"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'PolymarketMiddleEastFetcher/2.0'
        })

        # Database for intelligent storage
        self.db_path = db_path
        self.init_database()

        # Track processed trades to avoid duplicates
        self.processed_trade_hashes: Set[str] = set()

        # Calculate timestamp for 2 years ago
        two_years_ago = datetime.now() - timedelta(days=730)
        self.start_timestamp = int(two_years_ago.timestamp())

        logger.info(f"Initialized fetcher with database: {db_path}")
        logger.info(f"Fetching trades from: {datetime.fromtimestamp(self.start_timestamp).isoformat()}")

    def init_database(self):
        """Initialize SQLite database for intelligent storage"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            # Markets table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS markets (
                    id TEXT PRIMARY KEY,
                    slug TEXT UNIQUE,
                    title TEXT,
                    condition_id TEXT,
                    active BOOLEAN,
                    closed BOOLEAN,
                    liquidity REAL,
                    volume REAL,
                    start_date TEXT,
                    end_date TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # Trades table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS trades (
                    id TEXT PRIMARY KEY,
                    market_id TEXT,
                    market_slug TEXT,
                    market_title TEXT,
                    condition_id TEXT,
                    proxy_wallet TEXT,
                    side TEXT,
                    asset TEXT,
                    size REAL,
                    price REAL,
                    timestamp INTEGER,
                    outcome TEXT,
                    outcome_index INTEGER,
                    trader_name TEXT,
                    trader_pseudonym TEXT,
                    transaction_hash TEXT,
                    trade_hash TEXT UNIQUE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (market_id) REFERENCES markets (id)
                )
            ''')

            # Create indexes for better performance
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_trades_timestamp ON trades (timestamp)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_trades_market_id ON trades (market_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_trades_hash ON trades (trade_hash)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_markets_slug ON markets (slug)')

            conn.commit()
            logger.info("Database initialized successfully")

    @retry_on_failure(max_retries=3, delay=1.0)
    def _make_request(self, url: str, params: Dict[str, Any] = None):
        """Make HTTP request with retry logic"""
        response = self.session.get(url, params=params)
        response.raise_for_status()
        return response

    def rate_limit_delay(self, delay: float = 0.1):
        """Add delay to respect rate limits"""
        time.sleep(delay)
    
    def generate_trade_hash(self, trade_data: Dict[str, Any]) -> str:
        """Generate a unique hash for a trade to avoid duplicates"""
        # Create a unique identifier from key trade fields
        trade_key = f"{trade_data.get('conditionId', '')}-{trade_data.get('proxyWallet', '')}-{trade_data.get('timestamp', '')}-{trade_data.get('size', '')}-{trade_data.get('price', '')}"
        return hashlib.md5(trade_key.encode()).hexdigest()

    def fetch_middle_east_events(self, active: bool = True) -> List[Dict[str, Any]]:
        """Fetch Middle East events using the specific gamma-api endpoint"""
        logger.info(f"Fetching {'active' if active else 'closed'} Middle East events...")

        all_events = []
        offset = 0
        limit = 1000  # Max limit as shown in your example

        while True:
            try:
                url = f"{self.gamma_api_base}/events/pagination"
                params = {
                    'limit': limit,
                    'active': str(active).lower(),
                    'archived': 'false',
                    'tag_slug': 'middle-east',
                    'closed': str(not active).lower(),
                    'offset': offset
                }

                logger.info(f"Fetching events: offset={offset}, limit={limit}, active={active}")
                response = self._make_request(url, params)

                if response is None:
                    logger.error(f"Failed to fetch events at offset {offset} after retries")
                    break

                response_data = response.json()

                # Extract events from the 'data' field
                events_data = response_data.get('data', [])

                if not events_data:
                    logger.info(f"No more {'active' if active else 'closed'} events to fetch")
                    break

                all_events.extend(events_data)

                # Check pagination
                pagination = response_data.get('pagination', {})
                has_more = pagination.get('hasMore', False)

                if not has_more:
                    logger.info("Reached end of pagination")
                    break

                offset += limit
                self.rate_limit_delay()

            except requests.exceptions.RequestException as e:
                logger.error(f"Error fetching events at offset {offset}: {e}")
                time.sleep(5)  # Wait longer on error
                continue

        logger.info(f"Found {len(all_events)} {'active' if active else 'closed'} Middle East events")
        return all_events
    
    def extract_markets_from_events(self, events: List[Dict[str, Any]]) -> List[Market]:
        """Extract market information from events data"""
        markets = []

        for event in events:
            # Each event can have multiple markets
            event_markets = event.get('markets', [])

            for market_data in event_markets:
                try:
                    market = Market(
                        id=market_data.get('id', ''),
                        slug=market_data.get('slug', ''),
                        title=market_data.get('question', ''),
                        condition_id=market_data.get('conditionId', ''),
                        active=market_data.get('active', False),
                        closed=market_data.get('closed', False),
                        liquidity=float(market_data.get('liquidity', 0)),
                        volume=float(market_data.get('volume', 0)),
                        start_date=market_data.get('startDate', ''),
                        end_date=market_data.get('endDate', '')
                    )
                    markets.append(market)

                except (ValueError, TypeError) as e:
                    logger.warning(f"Error processing market {market_data.get('slug', 'Unknown')}: {e}")
                    continue

        logger.info(f"Extracted {len(markets)} markets from {len(events)} events")
        return markets

    def store_market_in_db(self, market: Market):
        """Store or update market in database"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            cursor.execute('''
                INSERT OR REPLACE INTO markets
                (id, slug, title, condition_id, active, closed, liquidity, volume, start_date, end_date, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
            ''', (
                market.id, market.slug, market.title, market.condition_id,
                market.active, market.closed, market.liquidity, market.volume,
                market.start_date, market.end_date
            ))

            conn.commit()

    def fetch_all_middle_east_markets(self) -> List[Market]:
        """Fetch all Middle East markets using the events endpoint"""
        logger.info("Starting to fetch all Middle East markets...")

        all_markets = []

        # Fetch active markets
        active_events = self.fetch_middle_east_events(active=True)
        active_markets = self.extract_markets_from_events(active_events)

        # Fetch closed markets
        closed_events = self.fetch_middle_east_events(active=False)
        closed_markets = self.extract_markets_from_events(closed_events)

        # Combine all markets
        all_markets.extend(active_markets)
        all_markets.extend(closed_markets)

        # Remove duplicates based on market ID
        unique_markets = {}
        for market in all_markets:
            if market.id not in unique_markets:
                unique_markets[market.id] = market
                # Store in database
                self.store_market_in_db(market)

        final_markets = list(unique_markets.values())
        logger.info(f"Found {len(final_markets)} unique Middle East markets ({len(active_markets)} active, {len(closed_markets)} closed)")

        return final_markets

    def store_trade_in_db(self, trade_data: Dict[str, Any], market: Market):
        """Store trade in database if not already exists"""
        trade_hash = self.generate_trade_hash(trade_data)

        # Skip if already processed
        if trade_hash in self.processed_trade_hashes:
            return False

        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            try:
                cursor.execute('''
                    INSERT OR IGNORE INTO trades
                    (id, market_id, market_slug, market_title, condition_id, proxy_wallet, side, asset,
                     size, price, timestamp, outcome, outcome_index, trader_name, trader_pseudonym,
                     transaction_hash, trade_hash)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    f"{trade_data.get('conditionId', '')}-{trade_data.get('timestamp', '')}-{trade_hash[:8]}",
                    market.id,
                    market.slug,
                    market.title,
                    trade_data.get('conditionId', ''),
                    trade_data.get('proxyWallet', ''),
                    trade_data.get('side', ''),
                    trade_data.get('asset', ''),
                    float(trade_data.get('size', 0)),
                    float(trade_data.get('price', 0)),
                    int(trade_data.get('timestamp', 0)),
                    trade_data.get('outcome', ''),
                    int(trade_data.get('outcomeIndex', 0)) if trade_data.get('outcomeIndex') is not None else None,
                    trade_data.get('name', ''),
                    trade_data.get('pseudonym', ''),
                    trade_data.get('transactionHash', ''),
                    trade_hash
                ))

                conn.commit()
                self.processed_trade_hashes.add(trade_hash)
                return True

            except (ValueError, TypeError, sqlite3.Error) as e:
                logger.warning(f"Error storing trade: {e}")
                return False

    def fetch_trades_for_market(self, market: Market) -> int:
        """Fetch all trades for a specific market with pagination and store in DB"""
        logger.info(f"Fetching trades for market: {market.slug}")

        total_trades = 0
        offset = 0
        limit = 500  # Max limit per request

        while True:
            try:
                url = f"{self.data_api_base}/trades"
                params = {
                    'market': market.condition_id,
                    'limit': limit,
                    'offset': offset,
                    'takerOnly': False  # Get both maker and taker trades
                }

                logger.info(f"Fetching trades for {market.slug}: offset={offset}, limit={limit}")
                response = self._make_request(url, params)

                if response is None:
                    logger.error(f"Failed to fetch trades for {market.slug} at offset {offset} after retries")
                    break

                trades_data = response.json()

                if not trades_data:
                    logger.info(f"No more trades for market {market.slug}")
                    break

                # Process and store trades
                batch_count = 0
                oldest_timestamp = float('inf')

                for trade in trades_data:
                    trade_timestamp = trade.get('timestamp', 0)
                    oldest_timestamp = min(oldest_timestamp, trade_timestamp)

                    # Only process trades from the last 2 years
                    if trade_timestamp >= self.start_timestamp:
                        if self.store_trade_in_db(trade, market):
                            batch_count += 1

                total_trades += batch_count
                logger.info(f"Stored {batch_count} new trades for {market.slug} (total: {total_trades})")

                # Check if we got fewer results than the limit (last page)
                if len(trades_data) < limit:
                    break

                # If all trades in this batch are older than 2 years, stop
                if oldest_timestamp < self.start_timestamp:
                    logger.info(f"Reached trades older than 2 years for market {market.slug}")
                    break

                offset += limit
                self.rate_limit_delay()

            except requests.exceptions.RequestException as e:
                logger.error(f"Error fetching trades for market {market.slug} at offset {offset}: {e}")
                time.sleep(5)  # Wait longer on error
                continue

        logger.info(f"Completed fetching {total_trades} trades for market {market.slug}")
        return total_trades

    def fetch_all_trades(self, markets: List[Market]) -> Dict[str, int]:
        """Fetch all trades for all Middle East markets and store in database"""
        logger.info(f"Starting to fetch trades for {len(markets)} Middle East markets...")

        trades_by_market = {}

        for i, market in enumerate(markets, 1):
            logger.info(f"Processing market {i}/{len(markets)}: {market.slug}")

            try:
                trade_count = self.fetch_trades_for_market(market)
                trades_by_market[market.slug] = trade_count

                # Add delay between markets to be respectful
                self.rate_limit_delay(0.5)

            except Exception as e:
                logger.error(f"Failed to fetch trades for market {market.slug}: {e}")
                trades_by_market[market.slug] = 0
                continue

        total_trades = sum(trades_by_market.values())
        logger.info(f"Completed fetching trades. Total: {total_trades} trades across {len(markets)} markets")

        return trades_by_market

    def export_data_from_db(self, output_dir: str = "exports") -> Dict[str, str]:
        """Export all data from database to various formats"""
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        exported_files = {}

        with sqlite3.connect(self.db_path) as conn:
            # Export markets to CSV
            markets_df = conn.execute('SELECT * FROM markets ORDER BY volume DESC').fetchall()
            markets_columns = [desc[0] for desc in conn.execute('SELECT * FROM markets LIMIT 1').description]

            markets_csv = os.path.join(output_dir, f"middle_east_markets_{timestamp}.csv")
            with open(markets_csv, 'w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                writer.writerow(markets_columns)
                writer.writerows(markets_df)
            exported_files['markets_csv'] = markets_csv

            # Export trades to CSV
            trades_df = conn.execute('''
                SELECT t.*, m.title as market_title_full
                FROM trades t
                LEFT JOIN markets m ON t.market_id = m.id
                ORDER BY t.timestamp DESC
            ''').fetchall()

            if trades_df:
                trades_columns = [desc[0] for desc in conn.execute('''
                    SELECT t.*, m.title as market_title_full
                    FROM trades t
                    LEFT JOIN markets m ON t.market_id = m.id
                    LIMIT 1
                ''').description]

                trades_csv = os.path.join(output_dir, f"middle_east_trades_{timestamp}.csv")
                with open(trades_csv, 'w', newline='', encoding='utf-8') as f:
                    writer = csv.writer(f)
                    writer.writerow(trades_columns)
                    writer.writerows(trades_df)
                exported_files['trades_csv'] = trades_csv

            # Export summary JSON
            summary = self.generate_summary_from_db()
            summary_json = os.path.join(output_dir, f"middle_east_summary_{timestamp}.json")
            with open(summary_json, 'w', encoding='utf-8') as f:
                json.dump(summary, f, indent=2, ensure_ascii=False)
            exported_files['summary_json'] = summary_json

        logger.info(f"Data exported to: {exported_files}")
        return exported_files

    def export_to_json(self, trades_by_market: Dict[str, Any], filename: str = None):
        """Export trades data to JSON file"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"polymarket_middle_east_trades_{timestamp}.json"

        logger.info(f"Exporting data to {filename}")

        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(trades_by_market, f, indent=2, ensure_ascii=False)

        logger.info(f"Data exported to {filename}")
        return filename

    def export_to_csv(self, trades_by_market: Dict[str, Any], filename: str = None):
        """Export trades data to CSV file (flattened)"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"polymarket_middle_east_trades_{timestamp}.csv"

        logger.info(f"Exporting flattened data to {filename}")

        # Flatten the data for CSV export
        flattened_trades = []
        for _, market_data in trades_by_market.items():
            market_info = market_data['market_info']
            for trade in market_data['trades']:
                flattened_trade = {
                    # Market information
                    'market_id': market_info['id'],
                    'market_slug': market_info['slug'],
                    'market_title': market_info['title'],
                    'market_condition_id': market_info['condition_id'],
                    'market_active': market_info['active'],
                    'market_closed': market_info['closed'],
                    'market_liquidity': market_info['liquidity'],
                    'market_volume': market_info['volume'],
                    'market_start_date': market_info['start_date'],
                    'market_end_date': market_info['end_date'],

                    # Trade information
                    'trade_proxy_wallet': trade.get('proxyWallet', ''),
                    'trade_side': trade.get('side', ''),
                    'trade_asset': trade.get('asset', ''),
                    'trade_condition_id': trade.get('conditionId', ''),
                    'trade_size': trade.get('size', 0),
                    'trade_price': trade.get('price', 0),
                    'trade_timestamp': trade.get('timestamp', 0),
                    'trade_datetime': datetime.fromtimestamp(trade.get('timestamp', 0)).isoformat() if trade.get('timestamp') else '',
                    'trade_title': trade.get('title', ''),
                    'trade_slug': trade.get('slug', ''),
                    'trade_outcome': trade.get('outcome', ''),
                    'trade_outcome_index': trade.get('outcomeIndex', ''),
                    'trader_name': trade.get('name', ''),
                    'trader_pseudonym': trade.get('pseudonym', ''),
                    'transaction_hash': trade.get('transactionHash', '')
                }
                flattened_trades.append(flattened_trade)

        # Write to CSV
        if flattened_trades:
            fieldnames = flattened_trades[0].keys()
            with open(filename, 'w', newline='', encoding='utf-8') as f:
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(flattened_trades)

        logger.info(f"Flattened data exported to {filename} ({len(flattened_trades)} rows)")
        return filename

    def generate_summary_report(self, trades_by_market: Dict[str, Any]) -> Dict[str, Any]:
        """Generate a summary report of the fetched data"""
        total_trades = sum(data['trade_count'] for data in trades_by_market.values())
        total_volume = 0
        total_markets = len(trades_by_market)

        market_summaries = []

        for market_slug, market_data in trades_by_market.items():
            market_info = market_data['market_info']
            trades = market_data['trades']

            if trades:
                market_volume = sum(trade.get('size', 0) * trade.get('price', 0) for trade in trades)
                total_volume += market_volume

                latest_trade = max(trades, key=lambda x: x.get('timestamp', 0))
                earliest_trade = min(trades, key=lambda x: x.get('timestamp', 0))

                market_summary = {
                    'market_slug': market_slug,
                    'market_title': market_info['title'],
                    'trade_count': len(trades),
                    'market_volume': market_volume,
                    'market_liquidity': market_info['liquidity'],
                    'earliest_trade': datetime.fromtimestamp(earliest_trade.get('timestamp', 0)).isoformat(),
                    'latest_trade': datetime.fromtimestamp(latest_trade.get('timestamp', 0)).isoformat(),
                    'active': market_info['active'],
                    'closed': market_info['closed']
                }
                market_summaries.append(market_summary)

        # Sort by trade count descending
        market_summaries.sort(key=lambda x: x['trade_count'], reverse=True)

        summary = {
            'fetch_timestamp': datetime.now().isoformat(),
            'total_markets': total_markets,
            'total_trades': total_trades,
            'total_volume': total_volume,
            'date_range': {
                'start': datetime.fromtimestamp(self.start_timestamp).isoformat(),
                'end': datetime.now().isoformat()
            },
            'top_markets_by_trades': market_summaries[:10],  # Top 10 markets
            'all_markets': market_summaries
        }

        return summary

    def generate_summary_from_db(self) -> Dict[str, Any]:
        """Generate a comprehensive summary report from database"""
        logger.info("Generating summary report from database...")

        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            # Get market statistics
            market_stats = cursor.execute('''
                SELECT
                    m.slug, m.title, m.id, m.condition_id, m.active, m.closed,
                    m.liquidity, m.volume as market_volume,
                    COUNT(t.id) as trade_count,
                    SUM(t.size * t.price) as trading_volume,
                    AVG(t.price) as avg_price,
                    AVG(t.size) as avg_size,
                    SUM(CASE WHEN t.side = 'BUY' THEN 1 ELSE 0 END) as buy_trades,
                    SUM(CASE WHEN t.side = 'SELL' THEN 1 ELSE 0 END) as sell_trades
                FROM markets m
                LEFT JOIN trades t ON m.id = t.market_id
                GROUP BY m.id
                ORDER BY trade_count DESC
            ''').fetchall()

            # Get overall statistics
            total_stats = cursor.execute('''
                SELECT
                    COUNT(DISTINCT m.id) as total_markets,
                    COUNT(t.id) as total_trades,
                    SUM(t.size * t.price) as total_volume,
                    MIN(t.timestamp) as earliest_trade,
                    MAX(t.timestamp) as latest_trade
                FROM markets m
                LEFT JOIN trades t ON m.id = t.market_id
            ''').fetchone()

            market_summaries = []
            for row in market_stats:
                market_summary = {
                    'market_slug': row[0],
                    'market_title': row[1],
                    'market_id': row[2],
                    'condition_id': row[3],
                    'active': bool(row[4]),
                    'closed': bool(row[5]),
                    'liquidity': float(row[6]) if row[6] else 0,
                    'market_volume': float(row[7]) if row[7] else 0,
                    'trade_count': int(row[8]) if row[8] else 0,
                    'trading_volume': float(row[9]) if row[9] else 0,
                    'avg_price': float(row[10]) if row[10] else 0,
                    'avg_size': float(row[11]) if row[11] else 0,
                    'buy_trades': int(row[12]) if row[12] else 0,
                    'sell_trades': int(row[13]) if row[13] else 0
                }
                market_summaries.append(market_summary)

            summary = {
                'fetch_timestamp': datetime.now().isoformat(),
                'total_markets': int(total_stats[0]) if total_stats[0] else 0,
                'total_trades': int(total_stats[1]) if total_stats[1] else 0,
                'total_volume': float(total_stats[2]) if total_stats[2] else 0,
                'date_range': {
                    'start': datetime.fromtimestamp(self.start_timestamp).isoformat(),
                    'end': datetime.now().isoformat(),
                    'earliest_trade': datetime.fromtimestamp(total_stats[3]).isoformat() if total_stats[3] else None,
                    'latest_trade': datetime.fromtimestamp(total_stats[4]).isoformat() if total_stats[4] else None
                },
                'top_markets_by_trades': market_summaries[:10],  # Top 10 markets
                'all_markets': market_summaries,
                'database_path': self.db_path
            }

            return summary

    def run(self):
        """Main execution function using the enhanced Middle East fetcher"""
        try:
            logger.info("Starting Enhanced Polymarket Middle East trades fetcher...")
            logger.info(f"Fetching trades from {datetime.fromtimestamp(self.start_timestamp).isoformat()} to present")
            logger.info(f"Database: {self.db_path}")

            # Step 1: Fetch all Middle East markets using the events endpoint
            markets = self.fetch_all_middle_east_markets()

            if not markets:
                logger.warning("No Middle East markets found!")
                return

            logger.info(f"Found {len(markets)} Middle East markets")

            # Step 2: Fetch trades for all Middle East markets and store in database
            trades_by_market = self.fetch_all_trades(markets)

            total_trades = sum(trades_by_market.values())
            if total_trades == 0:
                logger.warning("No trades found for Middle East markets!")
                return

            logger.info(f"Stored {total_trades} trades in database")

            # Step 3: Generate summary report from database
            summary = self.generate_summary_from_db()

            # Step 4: Export data from database
            exported_files = self.export_data_from_db()

            # Print summary
            logger.info("=" * 60)
            logger.info("FETCH COMPLETE - SUMMARY")
            logger.info("=" * 60)
            logger.info(f"Database: {summary['database_path']}")
            logger.info(f"Total markets processed: {summary['total_markets']}")
            logger.info(f"Total trades fetched: {summary['total_trades']}")
            logger.info(f"Total volume: ${summary['total_volume']:,.2f}")
            logger.info(f"Date range: {summary['date_range']['start']} to {summary['date_range']['end']}")

            if summary['date_range']['earliest_trade']:
                logger.info(f"Earliest trade: {summary['date_range']['earliest_trade']}")
                logger.info(f"Latest trade: {summary['date_range']['latest_trade']}")

            logger.info(f"Files exported:")
            for file_type, file_path in exported_files.items():
                logger.info(f"  - {file_type}: {file_path}")

            if summary['top_markets_by_trades']:
                logger.info("\nTop 5 markets by trade count:")
                for i, market in enumerate(summary['top_markets_by_trades'][:5], 1):
                    logger.info(f"  {i}. {market['market_title']} ({market['trade_count']} trades, ${market['trading_volume']:,.2f} volume)")

            logger.info("\nData is stored in SQLite database for further analysis.")
            logger.info("You can query the database directly or use the exported CSV/JSON files.")
            logger.info("=" * 60)

        except KeyboardInterrupt:
            logger.info("Fetch interrupted by user")
        except Exception as e:
            logger.error(f"Unexpected error during execution: {e}")
            raise


def main():
    """Entry point for the script"""
    try:
        fetcher = PolymarketFetcher()
        fetcher.run()
    except Exception as e:
        logger.error(f"Fatal error: {e}")
        return 1
    return 0


if __name__ == "__main__":
    exit(main())
